# restore-cursor

> Gracefully restore the CLI cursor on exit

Prevent the cursor you've hidden interactively from remaining hidden if the process crashes.

## Install

```
$ npm install restore-cursor
```

## Usage

```js
import restoreCursor from 'restore-cursor';

restoreCursor();
```

---

<div align="center">
	<b>
		<a href="https://tidelift.com/subscription/pkg/npm-restore-cursor?utm_source=npm-restore-cursor&utm_medium=referral&utm_campaign=readme">Get professional support for this package with a Tidelift subscription</a>
	</b>
	<br>
	<sub>
		Tidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.
	</sub>
</div>
