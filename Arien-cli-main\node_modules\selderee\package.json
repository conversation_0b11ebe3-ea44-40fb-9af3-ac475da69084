{"name": "selderee", "version": "0.11.0", "description": "Selectors decision tree - choose matching selectors, fast", "keywords": ["CSS", "selectors", "decision tree", "match"], "repository": {"type": "git", "url": "git+https://github.com/mxxii/selderee.git"}, "bugs": {"url": "https://github.com/mxxii/selderee/issues"}, "homepage": "https://github.com/mxxii/selderee", "author": "KillyMXI", "funding": "https://ko-fi.com/killymxi", "license": "MIT", "exports": {"import": "./lib/selderee.mjs", "require": "./lib/selderee.cjs"}, "type": "module", "main": "./lib/selderee.cjs", "module": "./lib/selderee.mjs", "types": "./lib/selderee.d.ts", "typedocMain": "./src/selderee.ts", "files": ["lib"], "sideEffects": false, "scripts": {"build:rollup": "rollup -c", "build:types": "tsc -d --emitDeclarationOnly --declarationDir ./lib", "build": "npm run clean && npm run build:rollup && npm run build:types", "clean": "<PERSON><PERSON><PERSON> lib"}, "dependencies": {"parseley": "^0.12.0"}}